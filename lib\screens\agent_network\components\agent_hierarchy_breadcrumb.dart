import 'package:flutter/material.dart';
import 'package:neorevv/utils/dotted_line_painter.dart';
import '../../../config/app_strings.dart' as AppStrings;
import '../../../config/constants.dart';
import '../../../config/responsive.dart';
import '../../../models/broker.dart';
import '../../../theme/app_theme.dart';
import '../../../theme/app_fonts.dart';
import '../../../models/agent.dart';

class AgentHierarchyBreadcrumb extends StatelessWidget {
  final Broker broker;
  final List<Agent> hierarchyPath;
  final Function(int) onNavigate;

  const AgentHierarchyBreadcrumb({
    super.key,
    required this.hierarchyPath,
    required this.onNavigate,
    required this.broker,
  });

  @override
  Widget build(BuildContext context) {
    final isSmallMobile = Responsive.isSmallMobile(context);
    return Row(
      children: [
        Text(
          AppStrings.agentHierarchy,
          style: AppFonts.semiBoldTextStyle(
            14,
            color: AppTheme.primaryTextColor,
          ),
        ),
        SizedBox(
          width: defaultPadding,
          height: isSmallMobile && hierarchyPath.length > 0 ? 20 : 0,
        ),
        Expanded(child: Row(children: _buildBreadcrumbItems())),
      ],
    );
  }

  List<Widget> _buildBreadcrumbItems() {
    List<Widget> items = [
      _heirarchyItem(hierarchyPath.isEmpty, -1, broker.name),
    ];
    // items.add();
    for (int i = 0; i < hierarchyPath.length; i++) {
      final agent = hierarchyPath[i];
      final isLast = i == hierarchyPath.length - 1;

      items.add(buildHorizontalDottedLine());

      items.add(_heirarchyItem(isLast, i, agent.name));
    }
    return items;
  }

  GestureDetector _heirarchyItem(bool isLast, int i, String name) {
    return GestureDetector(
      onTap: isLast || i < 0 ? null : () => onNavigate(i),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isLast ? AppTheme.roundIconColor : Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          name,
          style: AppFonts.regularTextStyle(
            12,
            color: isLast ? AppTheme.white : AppTheme.primaryTextColor,
          ),
        ),
      ),
    );
  }
}
