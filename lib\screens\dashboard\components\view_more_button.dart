import 'package:flutter/material.dart';
import '../../../theme/app_theme.dart';
import '../../../theme/app_fonts.dart';

class ViewMoreButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String text;
  final double iconSize;
  final double fontSize;
  final double spacing;
  
  const ViewMoreButton({
    Key? key,
    required this.onPressed,
    required this.text,
    this.iconSize = 14,
    this.fontSize = 14,
    this.spacing = 3,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        padding: EdgeInsets.zero,
        minimumSize: Size(0, 0),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Row(
        children: [
          Icon(
            Icons.arrow_forward_ios,
            color: AppTheme.viewMoreBlue,
            size: iconSize,
          ),
          SizedBox(width: spacing),
          Text(
            text,
            style: AppFonts.semiBoldTextStyle(
              fontSize,
              color: AppTheme.viewMoreBlue,
            ),
          ),
        ],
      ),
    );
  }
}
