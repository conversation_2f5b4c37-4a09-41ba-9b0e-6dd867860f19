import 'package:flutter/material.dart';
import 'package:neorevv/components/common/app_scaffold.dart';
import 'package:neorevv/screens/broker/broker_screen_details_hook.dart';
import 'package:neorevv/screens/broker/brokers_screen_hook.dart';
// import other screens as needed

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      tabs: const [
        Tab(text: 'Dashboard'),
        Tab(text: 'Brokers'),
        Tab(text: 'Agents'),
        // Add more tabs as needed
      ],
      tabViews: [
        // DashboardScreen(), // Replace with your dashboard screen
        BrokerScreenDetails(),
        // AgentsScreen(), // Replace with your agents screen
      ],
      initialTabIndex: 1, // Set default tab (e.g., Brokers)
      version: '1.0.0', // Set your app version
    );
  }
}
