import 'package:flutter/material.dart';
class TableContainerTitle {
  final String text;
  final IconData? icon;
  final Widget? customIcon;
  final TextStyle? style;
  final Widget? trailing;

  const TableContainerTitle({
    required this.text,
    this.icon,
    this.customIcon,
    this.style,
    this.trailing,
  });
}

// Column configuration for the table
class TableColumn {
  final String header;
  final double? width;
  final bool sortable;
  final TextAlign alignment;
  final Widget Function(
    dynamic value,
    int rowIndex,
    Map<String, dynamic> rowData,
  )?
  customBuilder;

  const TableColumn({
    required this.header,
    this.width,
    this.sortable = false,
    this.alignment = TextAlign.left,
    this.customBuilder,
  });
}

// Action button configuration
class TableAction {
  final IconData icon;
  final String tooltip;
  final Color? color;
  final void Function(int rowIndex, Map<String, dynamic> rowData) onPressed;
  final Widget Function(int rowIndex, Map<String, dynamic> rowData)?
  customBuilder;

  const TableAction({
    required this.icon,
    required this.tooltip,
    required this.onPressed,
    this.color,
    this.customBuilder,
  });
}

// Filter configuration
class TableFilter {
  final String key;
  final String label;
  final List<String> options;
  final String? selectedValue;

  const TableFilter({
    required this.key,
    required this.label,
    required this.options,
    this.selectedValue,
  });
}