import 'package:flutter/material.dart';

import '../models/agent.dart';
import '../models/broker.dart';
import '../models/info_card.dart';
import '../theme/app_theme.dart';
import 'constants.dart';

final List<InfoCardData> infoCards = [
  InfoCardData(
    title: "Total Brokers",
    value: "168",
    assetImage: '$iconAssetpath/brokers.png',
    iconColor: Colors.blue,
    subtitle: "Last Month",
    additionalInfo: "38",
  ),
  InfoCardData(
    title: "Total Agents",
    value: "2850",
    assetImage: '$iconAssetpath/agents.png',
    iconColor: Colors.blue,
    subtitle: "Last Month",
    additionalInfo: "125",
  ),
  InfoCardData(
    title: "Total Sales",
    value: "20K",
    assetImage: '$iconAssetpath/sales.png',
    iconColor: Colors.blue,
    subtitle: "Year",
    additionalInfo: "2025",
  ),
  InfoCardData(
    title: "Total Revenue",
    value: "\$250K",
    assetImage: '$iconAssetpath/revenue.png',
    iconColor: Colors.blue,
    subtitle: "Year",
    additionalInfo: "2025",
  ),
];

final agents = [
  ["Agent 1 long name test", "12 Sales", "\$2200"],
  ["Agent 2", "30 Sales", "\$2600"],
  ["Agent 3", "8 Sales", "\$1200"],
  ["Agent 4", "25 Sales", "\$900"],
  ["Agent 5", "13 Sales", "\$3800"],
  ["Agent 6", "10 Sales", "\$800"],
  ["Agent 7", "30 Sales", "\$5820"],
  ["Agent 4", "25 Sales", "\$900"],
  ["Agent 5", "13 Sales", "\$3800"],
  ["Agent 6", "10 Sales", "\$800"],
  ["Agent 7", "30 Sales", "\$5820"],
];

// sample data with hierarchical structure
final agentList = [
  Agent(
    name: "Sophia Turner",
    sales: 22,
    amount: 3400,
    commission: 510,
    contact: "(225) 555-0101",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.primaryColor,
    imageUrl: "$imageAssetpath/profile.png",
  ),
  Agent(
    name: "Liam Johnson",
    sales: 18,
    amount: 2900,
    commission: 430,
    contact: "(225) 555-0102",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.primaryBlueColor,
    imageUrl: "$imageAssetpath/profile.png",
  ),
  Agent(
    name: "Olivia Smith",
    sales: 25,
    amount: 4100,
    commission: 620,
    contact: "(225) 555-0103",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.commissionCardDarkColor,
    imageUrl: "$imageAssetpath/profile.png",
  ),
  Agent(
    name: "Noah Williams",
    sales: 20,
    amount: 3600,
    commission: 540,
    contact: "(225) 555-0104",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.primaryColor,
    imageUrl: "$imageAssetpath/profile.png",
  ),
  Agent(
    name: "Emma Brown",
    sales: 16,
    amount: 2700,
    commission: 400,
    contact: "(225) 555-0105",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.primaryBlueColor,
    imageUrl: "$imageAssetpath/profile.png",
  ),
  Agent(
    name: "Mason Davis",
    sales: 19,
    amount: 3200,
    commission: 480,
    contact: "(225) 555-0106",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.commissionCardDarkColor,
    imageUrl: "$imageAssetpath/profile.png",
  ),
  Agent(
    name: "Ava Miller",
    sales: 23,
    amount: 3900,
    commission: 590,
    contact: "(225) 555-0107",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.primaryColor,
    imageUrl: "$imageAssetpath/profile.png",
  ),
  Agent(
    name: "Elijah Wilson",
    sales: 21,
    amount: 3500,
    commission: 530,
    contact: "(225) 555-0108",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.primaryBlueColor,
    imageUrl: "$imageAssetpath/profile.png",
  ),
  Agent(
    name: "Isabella Moore",
    sales: 17,
    amount: 2800,
    commission: 420,
    contact: "(225) 555-0109",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.commissionCardDarkColor,
    imageUrl: "$imageAssetpath/profile.png",
  ),
  Agent(
    name: "James Taylor",
    sales: 15,
    amount: 2500,
    commission: 380,
    contact: "(225) 555-0110",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.primaryColor,
    imageUrl: "$imageAssetpath/profile.png",
  ),
  Agent(
    name: "Mia White",
    sales: 14,
    amount: 2300,
    commission: 350,
    contact: "(225) 555-0111",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.primaryBlueColor,
    imageUrl: "$imageAssetpath/profile.png",
  ),
  Agent(
    name: "Jackson Lee",
    sales: 13,
    amount: 2100,
    commission: 320,
    contact: "(225) 555-0112",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.commissionCardDarkColor,
    imageUrl: "$imageAssetpath/profile.png",
  ),
  Agent(
    name: "Amelia Robinson",
    sales: 12,
    amount: 2000,
    commission: 300,
    contact: "(225) 555-0113",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.primaryColor,
    imageUrl: "$imageAssetpath/profile.png",
  ),
];

final brokers = [
  Broker(
    name: "Charlotte Anderson",
    sales: 12,
    contact: "(415) 555-0101",
    email: "<EMAIL>",
    agents: agentList.sublist(0, 6),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 15000,
    totalCommission: 8000,
  ),
  Broker(
    name: "Benjamin Thomas",
    sales: 30,
    contact: "(415) 555-0102",
    email: "<EMAIL>",
    agents: agentList.sublist(4, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 32000,
    totalCommission: 12000,
  ),
  Broker(
    name: "Mia Jackson",
    sales: 8,
    contact: "(415) 555-0103",
    email: "<EMAIL>",
    agents: agentList.sublist(2, 8),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 9000,
    totalCommission: 3500,
  ),
  Broker(
    name: "William White",
    sales: 25,
    contact: "(415) 555-0104",
    email: "<EMAIL>",
    agents: agentList.sublist(3, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 25000,
    totalCommission: 10000,
  ),
  Broker(
    name: "Amelia Harris",
    sales: 13,
    contact: "(415) 555-0105",
    email: "<EMAIL>",
    agents: agentList.sublist(0, 6),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 14000,
    totalCommission: 6000,
  ),
  Broker(
    name: "Lucas Martin",
    sales: 10,
    contact: "(415) 555-0106",
    email: "<EMAIL>",
    agents: agentList.sublist(3, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 11000,
    totalCommission: 4000,
  ),
  Broker(
    name: "Harper Lee",
    sales: 30,
    contact: "(415) 555-0107",
    email: "<EMAIL>",
    agents: agentList.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 33000,
    totalCommission: 15000,
  ),
  Broker(
    name: "Henry Clark",
    sales: 58,
    contact: "(415) 555-0108",
    email: "<EMAIL>",
    agents: agentList.sublist(0, 10),
    imageUrl: "$iconAssetpath/broker7.png",
    totalSalesRevenue: 60000,
    totalCommission: 22000,
  ),
  Broker(
    name: "Evelyn Lewis",
    sales: 42,
    contact: "(415) 555-0109",
    email: "<EMAIL>",
    agents: agentList.sublist(0, 10),
    imageUrl: "$iconAssetpath/broker7.png",
    totalSalesRevenue: 42000,
    totalCommission: 17000,
  ),
  Broker(
    name: "Jack Walker",
    sales: 30,
    contact: "(415) 555-0110",
    email: "<EMAIL>",
    agents: agentList.sublist(0, 10),
    imageUrl: "$iconAssetpath/broker7.png",
    totalSalesRevenue: 31000,
    totalCommission: 12000,
  ),
  Broker(
    name: "Ella Hall",
    sales: 35,
    contact: "(415) 555-0111",
    email: "<EMAIL>",
    agents: agentList.sublist(0, 10),
    imageUrl: "$iconAssetpath/broker7.png",
    totalSalesRevenue: 35000,
    totalCommission: 14000,
  ),
  Broker(
    name: "Alexander Young",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentList.sublist(0, 10),
    imageUrl: "$iconAssetpath/broker7.png",
    totalSalesRevenue: 44000,
    totalCommission: 18000,
  ),
];
