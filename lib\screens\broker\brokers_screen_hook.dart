import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/components/common/broker_card_list.dart';
import 'package:neorevv/components/common/generic_data_table.dart';
import 'package:neorevv/components/common/table_container.dart';
import 'package:neorevv/config/app_strings.dart' as AppStrings;
import 'package:neorevv/config/app_strings.dart';
import 'package:neorevv/config/broker_data.dart';
import 'package:neorevv/config/broker_table_config.dart';
import 'package:neorevv/config/constants.dart';
import 'package:neorevv/theme/app_theme.dart';
import 'package:neorevv/config/responsive.dart';
import 'package:neorevv/screens/dashboard/components/header.dart';

class BrokersScreen extends HookWidget {
  /// Converts a BrokerData object to a table row map
  Map<String, dynamic> _brokerToTableRow(BrokerData broker) {
    return {
      AppStrings.brokerNameKey: broker.brokerName,
      AppStrings.contactsKey: broker.contacts,
      AppStrings.emailAddressKey: broker.emailAddress,
      AppStrings.joinDateKey: broker.joinDate,
      AppStrings.addressKey: broker.address,
      AppStrings.totalAgentsKey: broker.totalAgents,
      AppStrings.totalSalesVolumeKey: broker.totalSalesVolume,
    };
  }
  BrokersScreen({super.key});
  // TODO: STATE_MANAGEMENT - Consider moving to state management solution (Provider/Bloc)
  // String searchQuery = '';
  // List<BrokerData> filteredBrokers = BrokerConstants.brokers;
  @override
  Widget build(BuildContext context) {
    final filteredBrokersState = useState<List<BrokerData>>(BrokerConstants.brokers);
      final isWeb = MediaQuery.of(context).size.width > 800;

    // Use same horizontal padding as TableContainer (assumed 32.0, update if TableContainer uses a different value)
    const double tableHorizontalPadding = 32.0;
    return Scaffold(
      body: isWeb
        ? Column(
            children: [
              Padding(
                 padding: EdgeInsets.symmetric(
            horizontal: !isWeb ? 8 : webLayoutmargin,
            vertical: !isWeb ? 8 : defaultMargin,
          ),
                child: Header(selectedTab: AppStrings.brokersTab),
              ),
              Expanded(
                child: _buildBrokerTable(context, filteredBrokersState),
              ),
            ],
          )
        : _buildMobileBrokerView(filteredBrokersState),
    );
}
 Widget _buildBrokerTable(BuildContext context, ValueNotifier<List<BrokerData>> filteredBrokersState) {
    final columns = [
      TableColumn(
        header: AppStrings.brokerName,
        sortable: true,
        customBuilder: (value, rowIndex, rowData) {
          final brokerName = value?.toString() ?? '';
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: userIconSize,
                height: userIconSize,
                decoration: const BoxDecoration(
                  color: AppTheme.userIconBackgroundColor,
                  shape: BoxShape.circle,
                ),
                child: ClipOval(
                  child: Image.asset(
                    userIconPath,
                    width: userIconImageSize,
                    height: userIconImageSize,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              const SizedBox(width: brokerNameSpacing),
              Expanded(
                child: Text(
                  brokerName,
                  style: const TextStyle(
                    fontWeight: brokerNameTextWeight,
                    fontSize: brokerNameTextSize,
                    color: AppTheme.brokerNameTextColor,
                    fontFamily: AppStrings.fontFamily,
                    height: brokerNameLineHeight,
                    letterSpacing: brokerNameLetterSpacing,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          );
        },
      ),
      const TableColumn(header: AppStrings.contacts, sortable: true),
      const TableColumn(header: AppStrings.emailAddress, sortable: true),
      const TableColumn(header: AppStrings.joinDate, sortable: true),
      TableColumn(
        header: AppStrings.address,
        sortable: true,
        customBuilder: (value, rowIndex, rowData) {
          final address = value?.toString() ?? '';
          return Tooltip(
            message: address,
            child: Row(
              children: [
                Flexible(
                  child: Text(
                    address,
                    style: const TextStyle(
                      fontSize: dataTextSize,
                      height: 1.2,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    softWrap: false,
                  ),
                ),
              ],
            ),
          );
        },
      ),
      const TableColumn(
        header: AppStrings.totalAgents,
        sortable: true,
        alignment: TextAlign.left,
      ),
      const TableColumn(
        header: AppStrings.totalSalesVolume,
        sortable: true,
        alignment: TextAlign.left,
      ),
    ];

    final actions = [
      TableAction(
        icon: Icons.visibility_outlined,
        tooltip: AppStrings.viewDetails,
        color: AppTheme.primaryBlueColor,
        onPressed: (rowIndex, rowData) => _onBrokerAction(context, rowIndex, rowData),
        customBuilder: (rowIndex, rowData) {
          return Container(
            width: actionIconContainerSize,
            height: actionIconContainerSize,
            decoration: const BoxDecoration(
              color: AppTheme.userIconBackgroundColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Image.asset(
                eyeIconPath,
                width: sortIconSize,
                height: sortIconSize,
                fit: BoxFit.contain,
              ),
            ),
          );
        },
      ),
    ];

    return TableContainer(
      title: TableContainerTitle(
        text: AppStrings.brokersTitle,
        customIcon: Image.asset(
          userIconTablePath,
          width: titleIconSize,
          height: titleIconSize,
          color: AppTheme.titleIconColor,
        ),
      ),
      filterOptions: _buildFilterOptions(),
      columns: columns,
      data: filteredBrokersState.value
          .where((broker) => broker.brokerName.isNotEmpty)
          .map(_brokerToTableRow)
          .toList(),
      actions: actions,
      itemsPerPage: defaultItemsPerPage,
      emptyMessage: AppStrings.noBrokersFound,
      showPagination: true,
      onSort: (columnKey, ascending) {
        filteredBrokersState.value = _sortBrokers(filteredBrokersState.value, columnKey, ascending);
      },
    );
  }

  // TODO: MOBILE_UI - Add pull-to-refresh functionality
  // TODO: MOBILE_UI - Add floating action button for quick actions
  Widget _buildMobileBrokerView(ValueNotifier<List<BrokerData>> filteredBrokersState) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Padding(
        padding: const EdgeInsets.all(mobileScreenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title section
            _buildTitleSection(),
            const SizedBox(height: 16),

            // Filter and Search section
            _buildMobileFilterSection(),
            const SizedBox(height: 16),

            // Broker cards list
            Expanded(child: BrokerCardList(brokers: filteredBrokersState.value)),
          ],
        ),
      ),
    );
  }

  /// TODO: UI_COMPONENTS - Extract title section to reusable widget
  Widget _buildTitleSection() {
    return Row(
      children: [
        Image.asset(
          userIconTablePath,
          width: titleIconSize,
          height: titleIconSize,
          color: AppTheme.titleIconColor,
        ),
        const SizedBox(width: 8),
        Text(
          AppStrings.brokersTitle,
          style: const TextStyle(
            fontSize: titleFontSize,
            fontWeight: titleFontWeight,
            fontFamily: fontFamily,
            color: AppTheme.primaryColor
          ),
        ),
      ],
    );
  }
 // TODO: UI_COMPONENTS - Extract filter and search to separate widgets
  // TODO: FILTER_FUNCTIONALITY - Implement actual filter logic
  List<Widget> _buildFilterOptions() {
    return [_buildFilterButton(), _buildSearchField()];
  }

  /// TODO: UI_COMPONENTS - Convert to reusable widget component
  Widget _buildFilterButton() {
    return InkWell(
      onTap: () {
        // TODO: FILTER_FUNCTIONALITY - Implement filter dialog/dropdown
      },
      child: Container(
        height: filterButtonHeight,
        padding: const EdgeInsets.symmetric(
          horizontal: filterButtonPaddingHorizontal,
          vertical: filterButtonPaddingVertical,
        ),
        decoration: BoxDecoration(
          color: AppTheme.buttonBackgroundColor,
          borderRadius: BorderRadius.circular(
            searchFieldBorderRadius,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              filterIconPath,
              width: filterIconSize,
              height: filterIconSize,
              color: AppTheme.filterIconColor,
            ),
            const SizedBox(width: filterIconSpacing),
            const Text(
              AppStrings.filter,
              style: TextStyle(
                color: AppTheme.filterTextColor,
                fontSize: filterTextSize,
                fontFamily: AppStrings.fontFamily,
                fontWeight: filterTextWeight,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// TODO: UI_COMPONENTS - Convert to reusable widget component
  Widget _buildSearchField({bool isExpanded = false}) {
    return Container(
      height: filterButtonHeight,
      constraints: isExpanded
          ? null
          : const BoxConstraints(
              minWidth: searchFieldMinWidth,
              maxWidth: searchFieldMaxWidth,
            ),
      decoration: BoxDecoration(
        color: AppTheme.buttonBackgroundColor,
        borderRadius: BorderRadius.circular(
          searchFieldBorderRadius,
        ),
      ),
      child: TextField(
        onChanged: _onSearchChanged,
        style: const TextStyle(
          fontFamily: AppStrings.fontFamily,
          fontSize: searchTextSize,
          color: AppTheme.searchTextColor,
        ),
        decoration: InputDecoration(
          hintText: AppStrings.search,
          hintStyle: const TextStyle(
            color: AppTheme.searchHintColor,
            fontSize: searchHintTextSize,
            fontFamily: AppStrings.fontFamily,
            fontWeight: searchTextWeight,
          ),
          prefixIcon: Padding(
            padding: const EdgeInsets.only(
              left: searchIconPaddingLeft,
              top: searchIconPaddingVertical,
              bottom: searchIconPaddingVertical,
            ),
            child: Image.asset(
              searchIconPath,
              width: searchIconSize,
              height: searchIconSize,
              color: AppTheme.searchIconColor,
            ),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: searchContentPaddingHorizontal,
            vertical: searchContentPaddingVertical,
          ),
          isDense: true,
        ),
      ),
    );
  }

  /// TODO: UI_COMPONENTS - Optimize mobile layout for better UX
  Widget _buildMobileFilterSection() {
    return Row(
      children: [
        _buildFilterButton(),
        const SizedBox(width: 12),
        Expanded(child: _buildSearchField(isExpanded: true)),
      ],
    );
  }
   void _onSearchChanged(String query) {
    // To enable search, you can update filteredBrokersState.value here
  }

  /// TODO: OPTIMIZATION - Extract search logic for reusability
  List<BrokerData> _filterBrokers(String query) {
    if (query.isEmpty) {
      return BrokerConstants.brokers;
    }

    final lowercaseQuery = query.toLowerCase();
    return BrokerConstants.brokers.where((broker) {
      return broker.brokerName.toLowerCase().contains(lowercaseQuery) ||
          broker.emailAddress.toLowerCase().contains(lowercaseQuery) ||
          broker.address.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // TODO: NAVIGATION - Implement proper broker detail navigation
  // TODO: UI_FEEDBACK - Replace SnackBar with proper action (navigate to detail page)
  void _onBrokerAction(BuildContext context,int rowIndex, Map<String, dynamic> rowData) {
    final brokerName = rowData[AppStrings.brokerNameKey] ?? '';
    _showActionFeedback(context, brokerName);
  }

  /// TODO: UI_FEEDBACK - Extract feedback logic for reusability
  void _showActionFeedback(BuildContext context, String brokerName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${AppStrings.actionClickedFor}$brokerName'),
        duration: const Duration(seconds: snackBarDurationSeconds),
      ),
    );
  }

  // TODO: SORT_FUNCTIONALITY - Add sort indicators in column headers
  // TODO: SORT_FUNCTIONALITY - Add multi-column sorting capability
  // TODO: OPTIMIZATION - Extract sort logic to separate service class
  void _onSort(String columnKey, bool ascending) {
    // Now handled by useState in TableContainer's onSort
  }

  /// TODO: OPTIMIZATION - Extract sorting logic for reusability and testing
  List<BrokerData> _sortBrokers(
    List<BrokerData> brokers,
    String columnKey,
    bool ascending,
  ) {
    final sortedBrokers = List<BrokerData>.from(brokers);

    sortedBrokers.sort((a, b) {
      final comparison = _compareValues(a, b, columnKey);
      return ascending ? comparison : -comparison;
    });

    return sortedBrokers;
  }

  /// TODO: OPTIMIZATION - Add type-specific comparison (dates, numbers, strings)
  int _compareValues(BrokerData a, BrokerData b, String columnKey) {
    dynamic aValue = _getValueByColumn(a, columnKey);
    dynamic bValue = _getValueByColumn(b, columnKey);

    if (aValue == null && bValue == null) return 0;
    if (aValue == null) return -1;
    if (bValue == null) return 1;

    return aValue.toString().compareTo(bValue.toString());
  }

  /// TODO: OPTIMIZATION - Use reflection or map-based approach for better maintainability
  dynamic _getValueByColumn(BrokerData broker, String columnKey) {
    switch (columnKey.toLowerCase()) {
      case AppStrings.brokerNameSort:
        return broker.brokerName;
      case AppStrings.contactsSort:
        return broker.contacts;
      case AppStrings.emailAddressSort:
        return broker.emailAddress;
      case AppStrings.joinDateSort:
        return broker.joinDate;
      case AppStrings.addressSort:
        return broker.address;
      case AppStrings.totalAgentsSort:
        return broker.totalAgents;
      case AppStrings.totalSalesVolumeSort:
        return broker.totalSalesVolume;
      default:
        return '';
    }
  }

  // TODO: DATA_CONVERSION - Add data validation and error handling
  // TODO: OPTIMIZATION - Cache converted data to avoid repeated conversions
}







