import 'package:flutter/material.dart';
// import 'package:neorevv/config/constants.dart';
import 'package:neorevv/theme/app_theme.dart';

/// Common scaffold for all pages with shared header tabs and footer
class AppScaffold extends StatelessWidget {
  final List<Tab> tabs;
  final List<Widget> tabViews;
  final int initialTabIndex;
  final String version;

  const AppScaffold({
    super.key,
    required this.tabs,
    required this.tabViews,
    this.initialTabIndex = 0,
    required this.version,
  });

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: tabs.length,
      initialIndex: initialTabIndex,
      child: Scaffold(
        body: Column(
          children: [
            // Header Tabs
            Container(
              color: AppTheme.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              child: TabBar(
                tabs: tabs,
                indicatorColor: Colors.white,
                labelColor: Colors.white,
                unselectedLabelColor: Colors.white70,
                isScrollable: true,
              ),
            ),
            // Main content
            Expanded(
              child: TabBarView(
                children: tabViews,
              ),
            ),
            // Footer
            Container(
              color: Colors.grey[100],
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    'Version: $version',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
