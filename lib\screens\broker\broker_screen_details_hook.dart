import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/components/common/table_container.dart';
import 'package:neorevv/config/app_strings.dart' as AppStrings;
import 'package:neorevv/config/broker_data.dart';
import 'package:neorevv/config/broker_table_config.dart';
import 'package:neorevv/config/constants.dart';
import 'package:neorevv/screens/dashboard/components/header.dart';
import 'package:neorevv/theme/app_theme.dart';

class BrokerScreenDetails extends HookWidget {
  const BrokerScreenDetails({super.key});

  /// Converts a BrokerData object to a table row map
  Map<String, dynamic> _brokerToTableRow(BrokerData broker) {
    return {
      AppStrings.brokerNameKey: broker.brokerName,
      AppStrings.contactsKey: broker.contacts,
      AppStrings.emailAddressKey: broker.emailAddress,
      AppStrings.joinDateKey: broker.joinDate,
      AppStrings.addressKey: broker.address,
      AppStrings.totalAgentsKey: broker.totalAgents.toString(),
      AppStrings.totalSalesVolumeKey: broker.totalSalesVolume,
    };
  }

  @override
  Widget build(BuildContext context) {
    final filteredBrokersState = useState<List<BrokerData>>(BrokerConstants.brokers);
    final searchController = useTextEditingController();
    final isWeb = MediaQuery.of(context).size.width > 800;

    return Scaffold(
      body: isWeb
          ? Column(
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: !isWeb ? 8 : webLayoutmargin,
                    vertical: !isWeb ? 8 : defaultMargin,
                  ),
                  child: Header(selectedTab: 'Broker Details'),
                ),
                Expanded(
                  child: _buildDetailsTable(context, filteredBrokersState, searchController),
                ),
              ],
            )
          : _buildMobileDetailsView(context, filteredBrokersState, searchController),
    );
  }

  Widget _buildDetailsTable(BuildContext context, ValueNotifier<List<BrokerData>> filteredBrokersState, TextEditingController searchController) {
    final columns = [
      TableColumn(
        header: AppStrings.brokerName,
        sortable: true,
        customBuilder: (value, rowIndex, rowData) {
          final brokerName = value?.toString() ?? '';
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: userIconSize,
                height: userIconSize,
                decoration: const BoxDecoration(
                  color: AppTheme.userIconBackgroundColor,
                  shape: BoxShape.circle,
                ),
                child: ClipOval(
                  child: Image.asset(
                    userIconPath,
                    width: userIconImageSize,
                    height: userIconImageSize,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              const SizedBox(width: brokerNameSpacing),
              Expanded(
                child: Text(
                  brokerName,
                  style: const TextStyle(
                    fontWeight: brokerNameTextWeight,
                    fontSize: brokerNameTextSize,
                    color: AppTheme.brokerNameTextColor,
                    fontFamily: AppStrings.fontFamily,
                    height: brokerNameLineHeight,
                    letterSpacing: brokerNameLetterSpacing,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          );
        },
      ),
      const TableColumn(header: AppStrings.contacts, sortable: true),
      const TableColumn(header: AppStrings.emailAddress, sortable: true),
      const TableColumn(header: AppStrings.joinDate, sortable: true),
      TableColumn(
        header: AppStrings.address,
        sortable: true,
        customBuilder: (value, rowIndex, rowData) {
          final address = value?.toString() ?? '';
          return Tooltip(
            message: address,
            child: Row(
              children: [
                Flexible(
                  child: Text(
                    address,
                    style: const TextStyle(
                      fontSize: dataTextSize,
                      height: 1.2,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    softWrap: false,
                  ),
                ),
              ],
            ),
          );
        },
      ),
      const TableColumn(
        header: AppStrings.totalAgents,
        sortable: true,
        alignment: TextAlign.left,
      ),
      const TableColumn(
        header: AppStrings.totalSalesVolume,
        sortable: true,
        alignment: TextAlign.left,
      ),
    ];

    return TableContainer(
      title: TableContainerTitle(
        text: 'Broker Details',
        customIcon: Image.asset(
          userIconTablePath,
          width: titleIconSize,
          height: titleIconSize,
          color: AppTheme.titleIconColor,
        ),
      ),
      filterOptions: _buildFilterOptions(searchController, filteredBrokersState),
      columns: columns,
      data: filteredBrokersState.value
          .where((broker) => broker.brokerName.isNotEmpty)
          .map(_brokerToTableRow)
          .toList(),
      itemsPerPage: defaultItemsPerPage,
      emptyMessage: AppStrings.noBrokersFound,
      showPagination: true,
      onSort: (columnKey, ascending) {
        filteredBrokersState.value = _sortBrokers(filteredBrokersState.value, columnKey, ascending);
      },
    );
  }

  Widget _buildMobileDetailsView(BuildContext context, ValueNotifier<List<BrokerData>> filteredBrokersState, TextEditingController searchController) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Padding(
        padding: const EdgeInsets.all(mobileScreenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title section
            _buildTitleSection(),
            const SizedBox(height: 16),

            // Filter and Search section
            _buildMobileFilterSection(searchController, filteredBrokersState),
            const SizedBox(height: 16),

            // Broker details list
            Expanded(
              child: filteredBrokersState.value.isEmpty
                  ? Center(
                      child: Text(
                        AppStrings.noBrokersFound,
                        style: const TextStyle(
                          color: AppTheme.secondaryText,
                          fontSize: 16,
                        ),
                      ),
                    )
                  : ListView.builder(
                      itemCount: filteredBrokersState.value.length,
                      itemBuilder: (context, index) {
                        final broker = filteredBrokersState.value[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8.0),
                          child: ListTile(
                            leading: Container(
                              width: 40,
                              height: 40,
                              decoration: const BoxDecoration(
                                color: AppTheme.userIconBackgroundColor,
                                shape: BoxShape.circle,
                              ),
                              child: ClipOval(
                                child: Image.asset(
                                  userIconPath,
                                  width: 32,
                                  height: 32,
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                            title: Text(
                              broker.brokerName,
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 4),
                                Text(broker.emailAddress),
                                Text('Contact: ${broker.contacts}'),
                                Text('Agents: ${broker.totalAgents}'),
                                Text('Sales: ${broker.totalSalesVolume}'),
                                Text('Joined: ${broker.joinDate}'),
                              ],
                            ),
                            isThreeLine: true,
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  /// Title section for mobile view
  Widget _buildTitleSection() {
    return Row(
      children: [
        Image.asset(
          userIconTablePath,
          width: titleIconSize,
          height: titleIconSize,
          color: AppTheme.titleIconColor,
        ),
        const SizedBox(width: 8),
        Text(
          'Broker Details',
          style: const TextStyle(
            fontSize: titleFontSize,
            fontWeight: titleFontWeight,
            fontFamily: AppStrings.fontFamily,
            color: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }

  /// Filter options for desktop view
  List<Widget> _buildFilterOptions(TextEditingController searchController, ValueNotifier<List<BrokerData>> filteredBrokersState) {
    return [
      _buildFilterButton(),
      _buildSearchField(searchController, filteredBrokersState),
    ];
  }

  /// Mobile filter section
  Widget _buildMobileFilterSection(TextEditingController searchController, ValueNotifier<List<BrokerData>> filteredBrokersState) {
    return Row(
      children: [
        _buildFilterButton(),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSearchField(searchController, filteredBrokersState, isExpanded: true),
        ),
      ],
    );
  }

  /// Filter button widget
  Widget _buildFilterButton() {
    return InkWell(
      onTap: () {
        // TODO: Implement filter dialog/dropdown
      },
      child: Container(
        height: filterButtonHeight,
        padding: const EdgeInsets.symmetric(
          horizontal: filterButtonPaddingHorizontal,
          vertical: filterButtonPaddingVertical,
        ),
        decoration: BoxDecoration(
          color: AppTheme.buttonBackgroundColor,
          borderRadius: BorderRadius.circular(
            searchFieldBorderRadius,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              filterIconPath,
              width: filterIconSize,
              height: filterIconSize,
              color: AppTheme.filterIconColor,
            ),
            const SizedBox(width: filterIconSpacing),
            const Text(
              AppStrings.filter,
              style: TextStyle(
                color: AppTheme.filterTextColor,
                fontSize: filterTextSize,
                fontFamily: AppStrings.fontFamily,
                fontWeight: filterTextWeight,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Search field widget
  Widget _buildSearchField(TextEditingController searchController, ValueNotifier<List<BrokerData>> filteredBrokersState, {bool isExpanded = false}) {
    return Container(
      height: filterButtonHeight,
      constraints: isExpanded
          ? null
          : const BoxConstraints(
              minWidth: searchFieldMinWidth,
              maxWidth: searchFieldMaxWidth,
            ),
      decoration: BoxDecoration(
        color: AppTheme.buttonBackgroundColor,
        borderRadius: BorderRadius.circular(
          searchFieldBorderRadius,
        ),
      ),
      child: TextField(
        controller: searchController,
        onChanged: (query) => _performSearch(query, filteredBrokersState),
        style: const TextStyle(
          fontFamily: AppStrings.fontFamily,
          fontSize: searchTextSize,
          color: AppTheme.searchTextColor,
        ),
        decoration: InputDecoration(
          hintText: AppStrings.search,
          hintStyle: const TextStyle(
            color: AppTheme.searchHintColor,
            fontSize: searchHintTextSize,
            fontFamily: AppStrings.fontFamily,
            fontWeight: searchTextWeight,
          ),
          prefixIcon: Padding(
            padding: const EdgeInsets.only(
              left: searchIconPaddingLeft,
              top: searchIconPaddingVertical,
              bottom: searchIconPaddingVertical,
            ),
            child: Image.asset(
              searchIconPath,
              width: searchIconSize,
              height: searchIconSize,
              color: AppTheme.searchIconColor,
            ),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: searchContentPaddingHorizontal,
            vertical: searchContentPaddingVertical,
          ),
          isDense: true,
        ),
      ),
    );
  }

  /// Perform search filtering
  void _performSearch(String query, ValueNotifier<List<BrokerData>> filteredBrokersState) {
    if (query.isEmpty) {
      filteredBrokersState.value = BrokerConstants.brokers;
    } else {
      final lowercaseQuery = query.toLowerCase();
      filteredBrokersState.value = BrokerConstants.brokers.where((broker) {
        return broker.brokerName.toLowerCase().contains(lowercaseQuery) ||
               broker.emailAddress.toLowerCase().contains(lowercaseQuery) ||
               broker.contacts.toLowerCase().contains(lowercaseQuery) ||
               broker.address.toLowerCase().contains(lowercaseQuery) ||
               broker.totalSalesVolume.toLowerCase().contains(lowercaseQuery);
      }).toList();
    }
  }

  /// Sort brokers by column
  List<BrokerData> _sortBrokers(
    List<BrokerData> brokers,
    String columnKey,
    bool ascending,
  ) {
    final sortedBrokers = List<BrokerData>.from(brokers);

    sortedBrokers.sort((a, b) {
      final comparison = _compareValues(a, b, columnKey);
      return ascending ? comparison : -comparison;
    });

    return sortedBrokers;
  }

  /// Compare values for sorting
  int _compareValues(BrokerData a, BrokerData b, String columnKey) {
    dynamic aValue = _getValueByColumn(a, columnKey);
    dynamic bValue = _getValueByColumn(b, columnKey);

    if (aValue == null && bValue == null) return 0;
    if (aValue == null) return -1;
    if (bValue == null) return 1;

    return aValue.toString().compareTo(bValue.toString());
  }

  /// Get value by column key for sorting
  dynamic _getValueByColumn(BrokerData broker, String columnKey) {
    switch (columnKey.toLowerCase()) {
      case AppStrings.brokerNameSort:
        return broker.brokerName;
      case AppStrings.contactsSort:
        return broker.contacts;
      case AppStrings.emailAddressSort:
        return broker.emailAddress;
      case AppStrings.joinDateSort:
        return broker.joinDate;
      case AppStrings.addressSort:
        return broker.address;
      case AppStrings.totalAgentsSort:
        return broker.totalAgents;
      case AppStrings.totalSalesVolumeSort:
        return broker.totalSalesVolume;
      default:
        return '';
    }
  }
}
