import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/components/common/table_container.dart';
import 'package:neorevv/config/app_strings.dart' as AppStrings;
import 'package:neorevv/config/broker_data.dart';
import 'package:neorevv/config/broker_table_config.dart' show TableColumn, TableContainerTitle;
import 'package:neorevv/config/constants.dart';
import 'package:neorevv/theme/app_theme.dart';

class BrokerScreenDetails extends HookWidget {
  BrokerScreenDetails({super.key});

  // Converts BrokerData to table row
  Map<String, dynamic> _brokerToTableRow(BrokerData broker) {
    return {
      AppStrings.brokerNameKey: broker.brokerName,
      AppStrings.contactsKey: broker.contacts,
      AppStrings.emailAddressKey: broker.emailAddress,
      AppStrings.joinDateKey: broker.joinDate,
      AppStrings.addressKey: broker.address,
      AppStrings.totalAgentsKey: broker.totalAgents.toString(),
      AppStrings.totalSalesVolumeKey: broker.totalSalesVolume,
      // Add additional fields here as needed
    };
  }

  @override
  Widget build(BuildContext context) {
    final filteredBrokersState = useState<List<BrokerData>>(BrokerConstants.brokers);
    final searchController = useTextEditingController();
    final isWeb = MediaQuery.of(context).size.width > 800;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: isWeb
          ? _buildDetailsTable(context, filteredBrokersState, searchController)
          : _buildMobileDetailsView(context, filteredBrokersState, searchController),
    );
  }

  Widget _buildDetailsTable(BuildContext context, ValueNotifier<List<BrokerData>> filteredBrokersState, TextEditingController searchController) {
    final columns = [
      TableColumn(header: AppStrings.brokerNameKey, sortable: true),
      TableColumn(header: AppStrings.contactsKey, sortable: true),
      TableColumn(header: AppStrings.emailAddressKey, sortable: true),
      TableColumn(header: AppStrings.joinDateKey, sortable: true),
      TableColumn(header: AppStrings.addressKey, sortable: true),
      TableColumn(header: AppStrings.totalAgentsKey, sortable: true),
      TableColumn(header: AppStrings.totalSalesVolumeKey, sortable: true),
      // Add additional TableColumn for extra fields
    ];

    return TableContainer(
      title: TableContainerTitle(
        text: 'Broker Details',
        customIcon: Icon(Icons.info_outline, color: AppTheme.titleIconColor),
      ),
      filterOptions: _buildFilterOptions(searchController, filteredBrokersState),
      columns: columns,
      data: filteredBrokersState.value.map(_brokerToTableRow).toList(),
      itemsPerPage: defaultItemsPerPage,
      emptyMessage: 'No broker details found.',
      showPagination: true,
      onSort: (columnKey, ascending) {
        filteredBrokersState.value = _sortBrokers(filteredBrokersState.value, columnKey, ascending);
      },
    );
  }

  Widget _buildMobileDetailsView(BuildContext context, ValueNotifier<List<BrokerData>> filteredBrokersState, TextEditingController searchController) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Broker Details', style: Theme.of(context).textTheme.titleLarge),
          const SizedBox(height: 16),
          ..._buildFilterOptions(searchController, filteredBrokersState),
          const SizedBox(height: 16),
          Expanded(
            child: filteredBrokersState.value.isEmpty
                ? const Center(
                    child: Text(
                      'No broker details found.',
                      style: TextStyle(
                        color: AppTheme.secondaryText,
                        fontSize: 16,
                      ),
                    ),
                  )
                : ListView.builder(
                    itemCount: filteredBrokersState.value.length,
                    itemBuilder: (context, index) {
                      final broker = filteredBrokersState.value[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8.0),
                        child: ListTile(
                          title: Text(broker.brokerName),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(broker.emailAddress),
                              Text('Agents: ${broker.totalAgents}'),
                              Text('Sales: ${broker.totalSalesVolume}'),
                            ],
                          ),
                          isThreeLine: true,
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildFilterOptions(TextEditingController searchController, ValueNotifier<List<BrokerData>> filteredBrokersState) {
    return [
      _buildFilterButton(),
      _buildSearchField(searchController, filteredBrokersState),
    ];
  }

  Widget _buildFilterButton() {
    return InkWell(
      onTap: () {
        // TODO: Implement filter dialog/dropdown
      },
      child: Container(
        height: 40,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: AppTheme.buttonBackgroundColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.filter_list, color: AppTheme.filterIconColor),
            const SizedBox(width: 8),
            const Text('Filter', style: TextStyle(fontWeight: FontWeight.w500)),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchField(TextEditingController searchController, ValueNotifier<List<BrokerData>> filteredBrokersState) {
    return Container(
      height: 40,
      margin: const EdgeInsets.only(left: 12),
      decoration: BoxDecoration(
        color: AppTheme.buttonBackgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextField(
        controller: searchController,
        onChanged: (query) {
          _performSearch(query, filteredBrokersState);
        },
        decoration: const InputDecoration(
          hintText: 'Search brokers...',
          border: InputBorder.none,
          prefixIcon: Icon(Icons.search),
        ),
      ),
    );
  }

  void _performSearch(String query, ValueNotifier<List<BrokerData>> filteredBrokersState) {
    if (query.isEmpty) {
      filteredBrokersState.value = BrokerConstants.brokers;
    } else {
      final lowercaseQuery = query.toLowerCase();
      filteredBrokersState.value = BrokerConstants.brokers.where((broker) {
        return broker.brokerName.toLowerCase().contains(lowercaseQuery) ||
               broker.emailAddress.toLowerCase().contains(lowercaseQuery) ||
               broker.contacts.toLowerCase().contains(lowercaseQuery) ||
               broker.address.toLowerCase().contains(lowercaseQuery) ||
               broker.totalSalesVolume.toLowerCase().contains(lowercaseQuery);
      }).toList();
    }
  }

  List<BrokerData> _sortBrokers(List<BrokerData> brokers, String columnKey, bool ascending) {
    final sortedBrokers = List<BrokerData>.from(brokers);
    sortedBrokers.sort((a, b) {
      final aValue = _getValueByColumn(a, columnKey);
      final bValue = _getValueByColumn(b, columnKey);
      return ascending
          ? aValue.toString().compareTo(bValue.toString())
          : bValue.toString().compareTo(aValue.toString());
    });
    return sortedBrokers;
  }

  dynamic _getValueByColumn(BrokerData broker, String columnKey) {
    switch (columnKey) {
      case AppStrings.brokerNameKey:
        return broker.brokerName;
      case AppStrings.contactsKey:
        return broker.contacts;
      case AppStrings.emailAddressKey:
        return broker.emailAddress;
      case AppStrings.joinDateKey:
        return broker.joinDate;
      case AppStrings.addressKey:
        return broker.address;
      case AppStrings.totalAgentsKey:
        return broker.totalAgents;
      case AppStrings.totalSalesVolumeKey:
        return broker.totalSalesVolume;
      default:
        return '';
    }
  }
}
