import 'package:flutter/material.dart';
import 'package:neorevv/config/app_strings.dart';
import 'package:neorevv/config/broker_table_config.dart';
import 'package:neorevv/config/constants.dart';
import 'package:neorevv/theme/app_theme.dart';

/// TODO: GENERIC_TABLE - Main reusable data table widget
/// - Supports responsive design (desktop/mobile)
/// - Handles pagination, sorting, filtering, and search
/// - Customizable columns with builders and actions
/// - Consistent styling across the application
class GenericDataTable extends StatefulWidget {
  // TODO: TABLE_STRUCTURE - Core table configuration
  final List<TableColumn> columns;
  final List<Map<String, dynamic>> data;
  final List<TableAction>? actions;
  final List<TableFilter>? filters;

  // TODO: PAGINATION_CONFIG - Pagination settings
  final int itemsPerPage;
  final bool showPagination;

  // TODO: DISPLAY_CONFIG - Display and messaging options
  final String? emptyMessage;
  final bool showSearch;
  final String? searchHint;
  final List<String>? searchableFields;

  // TODO: EVENT_HANDLERS - Callback functions for user interactions
  final void Function(String)? onSearch;
  final void Function(String, String?)? onFilter;
  final void Function(String, bool)? onSort;

  const GenericDataTable({
    super.key,
    required this.columns,
    required this.data,
    this.actions,
    this.filters,
    this.itemsPerPage = 10,
    this.emptyMessage,
    this.showPagination = true,
    this.showSearch = false,
    this.searchHint,
    this.searchableFields,
    this.onSearch,
    this.onFilter,
    this.onSort,
  });

  @override
  State<GenericDataTable> createState() => _GenericDataTableState();
}

class _GenericDataTableState extends State<GenericDataTable> {
  // TODO: PAGINATION_STATE - Pagination state management
  int _currentPage = 1;

  // TODO: SORTING_STATE - Column sorting state
  String _sortColumn = '';
  bool _sortAscending = true;

  // TODO: SCROLL_CONTROLLERS - Scroll management for responsive table
  late ScrollController _horizontalScrollController;
  late ScrollController _verticalScrollController;

  // TODO: INITIALIZATION - Initialize scroll controllers and state
  @override
  void initState() {
    super.initState();
    _horizontalScrollController = ScrollController();
    _verticalScrollController = ScrollController();
  }

  // TODO: CLEANUP - Dispose scroll controllers to prevent memory leaks
  @override
  void dispose() {
    _horizontalScrollController.dispose();
    _verticalScrollController.dispose();
    super.dispose();
  }

  // TODO: PAGINATION_CALCULATION - Calculate total pages based on data length
  int get _totalPages => widget.showPagination
      ? (widget.data.length / widget.itemsPerPage).ceil()
      : 1;

  // TODO: DATA_FILTERING - Get current page data with empty row filtering
  // TODO: OPTIMIZATION - Consider caching filtered data for better performance
  List<Map<String, dynamic>> get _currentPageData {
    if (!widget.showPagination) {
      // TODO: DATA_VALIDATION - Extract data validation logic to separate method
      return widget.data
          .where(
            (item) =>
                item.isNotEmpty &&
                item.values.any(
                  (value) =>
                      value != null && value.toString().trim().isNotEmpty,
                ),
          )
          .toList();
    }

    // TODO: PAGINATION_LOGIC - Extract pagination logic for reusability
    final startIndex = (_currentPage - 1) * widget.itemsPerPage;
    final endIndex = startIndex + widget.itemsPerPage;
    final result = widget.data.sublist(
      startIndex,
      endIndex > widget.data.length ? widget.data.length : endIndex,
    );

    // Filter out any null or empty entries
    return result
        .where(
          (item) =>
              item.isNotEmpty &&
              item.values.any(
                (value) => value != null && value.toString().trim().isNotEmpty,
              ),
        )
        .toList();
  }

  // TODO: NAVIGATION - Page navigation with bounds checking
  // TODO: ANIMATION - Add page transition animations
  void _goToPage(int page) {
    if (page >= 1 && page <= _totalPages) {
      setState(() {
        _currentPage = page;
      });
    }
  }

  // TODO: NAVIGATION - Previous page navigation
  void _previousPage() {
    if (_currentPage > 1) {
      _goToPage(_currentPage - 1);
    }
  }

  // TODO: NAVIGATION - Next page navigation
  void _nextPage() {
    if (_currentPage < _totalPages) {
      _goToPage(_currentPage + 1);
    }
  }

  // TODO: SORTING - Handle column sorting with toggle functionality
  // TODO: SORT_INDICATORS - Add visual sort indicators to column headers
  void _handleSort(String columnKey) {
    if (widget.onSort != null) {
      setState(() {
        if (_sortColumn == columnKey) {
          _sortAscending = !_sortAscending;
        } else {
          _sortColumn = columnKey;
          _sortAscending = true;
        }
      });
      widget.onSort!(columnKey, _sortAscending);
    }
  }

  // TODO: CELL_BUILDER - Build individual table cells with custom content
  // TODO: CELL_TYPES - Add support for different cell types (number, date, currency, etc.)
  // TODO: CELL_VALIDATION - Add cell content validation and formatting
  Widget _buildCell(
    TableColumn column,
    dynamic value,
    int rowIndex,
    Map<String, dynamic> rowData,
  ) {
    // TODO: CUSTOM_BUILDERS - Handle custom cell builders
    if (column.customBuilder != null) {
      return column.customBuilder!(value, rowIndex, rowData);
    }

    final text = value?.toString() ?? '';
    final hasMultipleLines = text.contains('\n');

    // TODO: COLUMN_DETECTION - Extract column type detection to separate method
    final isAddressColumn = column.header.toLowerCase().contains('address');

    // TODO: EMPTY_STATE - Handle empty cell content with consistent styling
    if (text.isEmpty) {
      return Container(
        // TODO: ALIGNMENT - Extract alignment logic to separate method
        alignment: column.alignment == TextAlign.center
            ? Alignment.center
            : column.alignment == TextAlign.right
            ? Alignment.centerRight
            : Alignment.centerLeft,
        child: const Text(
           emptyStateIndicator,
          style: TextStyle(
            fontSize:  dataTextSize,
            fontWeight: FontWeight.w500,
            color: AppTheme.emptyStateTextColor,
            fontFamily: fontFamily,
          ),
        ),
      );
    }

    return Container(
      alignment: column.alignment == TextAlign.center
          ? Alignment.center
          : column.alignment == TextAlign.right
          ? Alignment.centerRight
          : Alignment.centerLeft,
      child: Tooltip(
        message: text,
        child: Text(
          text,
          overflow: TextOverflow.ellipsis,
          maxLines: isAddressColumn || hasMultipleLines ? 2 : 1,
          softWrap: true,
          textAlign: column.alignment,
          style: const TextStyle(
            fontSize: dataTextSize,
            fontWeight: FontWeight.w500,
            color: AppTheme.dataTextColor,
            fontFamily: fontFamily,
          ),
        ),
      ),
    );
  }

  Widget _buildActionsCell(int rowIndex, Map<String, dynamic> rowData) {
    if (widget.actions == null || widget.actions!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: widget.actions!.asMap().entries.map((entry) {
        final index = entry.key;
        final action = entry.value;

        return Padding(
          padding: EdgeInsets.only(
            right: index < widget.actions!.length - 1
                ? actionButtonSpacing
                : 0,
          ),
          child: action.customBuilder != null
              ? GestureDetector(
                  onTap: () => action.onPressed(rowIndex, rowData),
                  child: Tooltip(
                    message: action.tooltip,
                    child: action.customBuilder!(rowIndex, rowData),
                  ),
                )
              : IconButton(
                  onPressed: () => action.onPressed(rowIndex, rowData),
                  icon: Icon(
                    action.icon,
                    color: action.color ?? AppTheme.primaryColor,
                    size: actionIconSize,
                  ),
                  tooltip: action.tooltip,
                ),
        );
      }).toList(),
    );
  }

  // TODO: MAIN_BUILD - Main widget build method with responsive layout
  // TODO: LOADING_STATES - Add loading and error states
  // TODO: ACCESSIBILITY - Add accessibility features (screen reader support, etc.)
  @override
  Widget build(BuildContext context) {
    // TODO: EMPTY_STATE - Handle empty data state with customizable message
    if (widget.data.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(emptyStatePadding),
          child: Text(
            widget.emptyMessage ?? noDataAvailable,
            style: const TextStyle(
              color: AppTheme.secondaryText,
              fontSize: emptyStateTextSize,
            ),
          ),
        ),
      );
    }

    // TODO: LAYOUT_STRUCTURE - Main table layout with data and pagination
    return Column(
      children: [
        // TODO: TABLE_SECTION - Data table with responsive scrolling
        Expanded(child: _buildDataTable()),

        // TODO: PAGINATION_SECTION - Conditional pagination controls
        if (widget.showPagination) ...[_buildPaginationSection()],
      ],
    );
  }

  // TODO: DATA_TABLE_BUILDER - Build responsive data table with scrolling
  // TODO: RESPONSIVE_TABLE - Add tablet and mobile optimizations
  // TODO: TABLE_PERFORMANCE - Optimize table rendering for large datasets
  Widget _buildDataTable() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTabletOrSmaller = screenWidth < tabletBreakpoint;

    // TODO: COLUMN_PREPARATION - Prepare columns with actions column
    final allColumns = [
      ...widget.columns,
      if (widget.actions != null && widget.actions!.isNotEmpty)
        const TableColumn(header: actions, sortable: false),
    ];

    // TODO: WIDTH_CALCULATION - Calculate responsive table width
    final minTableWidth = _calculateMinTableWidth(
      allColumns,
      isTabletOrSmaller,
    );

    Widget dataTable = DataTable(
      columnSpacing: isTabletOrSmaller ? 12 : columnSpacing,
      horizontalMargin: 0,
      dataRowMinHeight: dataRowMinHeight,
      dataRowMaxHeight: dataRowMaxHeight,
      headingRowHeight: headingRowHeight,
      dataTextStyle: const TextStyle(
        fontSize: dataTextSize,
        fontWeight: FontWeight.w500,
        color: AppTheme.dataTextColor,
        fontFamily: fontFamily,
        height: dataTextHeight,
        letterSpacing: dataTextLetterSpacing,
      ),
      dividerThickness: tableDividerThickness,
      border: TableBorder(
        bottom: BorderSide(
          color: AppTheme.tableBorderColor,
          width: tableBorderWidth,
        ),
        horizontalInside: BorderSide(
          color: AppTheme.tableBorderColor,
          width: tableBorderWidth,
        ),
      ),
      sortColumnIndex: null,
      sortAscending: _sortAscending,
      columns: allColumns.map((column) {
        final isActionsColumn = column.header == actions;
        final isCurrentSortColumn = column.header == _sortColumn;

        return DataColumn(
          label: Container(
            constraints: BoxConstraints(
              minWidth: _getColumnMinWidth(column, isTabletOrSmaller),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: isTabletOrSmaller
                    ? cellPaddingHorizontalTablet
                    : cellPaddingHorizontalDesktop,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Text(
                      column.header,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: headerTextSize,
                        color: AppTheme.tableHeaderColor,
                        fontFamily: fontFamily,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (!isActionsColumn && column.sortable) ...[
                    const SizedBox(width: 4),
                    GestureDetector(
                      onTap: () => _handleSort(column.header),
                      child: Image.asset(
                        sortIconPath,
                        width: sortIconSize,
                        height: sortIconSize,
                        color: isCurrentSortColumn
                            ? AppTheme.primaryColor
                            : AppTheme.sortIconInactiveColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      }).toList(),
      rows: _currentPageData
          .asMap()
          .entries
          .where((entry) {
            final rowData = entry.value;
            if (rowData.isEmpty) return false;

            final nonActionValues = rowData.entries
                .where((e) => e.key != actionsKey)
                .map((e) => e.value);

            return nonActionValues.any(
              (value) => value != null && value.toString().trim().isNotEmpty,
            );
          })
          .map((entry) {
            final rowIndex = entry.key;
            final rowData = entry.value;

            return DataRow(
              cells: allColumns.map((column) {
                if (column.header == actions) {
                  return DataCell(
                    Container(
                      width: _getColumnMinWidth(column, isTabletOrSmaller),
                      height: dataRowMinHeight,
                      padding: const EdgeInsets.symmetric(
                        horizontal: actionColumnPadding,
                      ),
                      child: Center(
                        child: _buildActionsCell(rowIndex, rowData),
                      ),
                    ),
                  );
                }

                final columnKey = column.header.toLowerCase().replaceAll(
                  spaceSeparator,
                  underscoreSeparator,
                );
                final value = rowData[columnKey];

                return DataCell(
                  Container(
                    constraints: BoxConstraints(
                      minWidth: _getColumnMinWidth(column, isTabletOrSmaller),
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: isTabletOrSmaller
                            ? cellPaddingHorizontalTablet
                            : cellPaddingHorizontalDesktop,
                      ),
                      child: _buildCell(column, value, rowIndex, rowData),
                    ),
                  ),
                );
              }).toList(),
            );
          })
          .toList(),
    );

    // Wrap table with proper scrolling behavior
    return _buildScrollableTable(dataTable, minTableWidth, isTabletOrSmaller);
  }

  double _calculateMinTableWidth(
    List<TableColumn> columns,
    bool isTabletOrSmaller,
  ) {
    double totalWidth = 0;
    for (final column in columns) {
      totalWidth += _getColumnMinWidth(column, isTabletOrSmaller);
    }
    // Add spacing between columns
    totalWidth +=
        (columns.length - 1) *
        (isTabletOrSmaller ? 12 : columnSpacing);
    return totalWidth;
  }

  double _getColumnMinWidth(TableColumn column, bool isTabletOrSmaller) {
    // Define minimum widths for different column types - ensure adequate space for tablet scrolling
    switch (column.header.toLowerCase()) {
      case 'actions':
        return isTabletOrSmaller
            ? actionsColumnWidthTablet
            : actionsColumnWidthDesktop;
      case 'broker name':
        return isTabletOrSmaller
            ? brokerNameColumnWidthTablet
            : brokerNameColumnWidthDesktop;
      case 'contacts':
        return isTabletOrSmaller
            ? contactsColumnWidthTablet
            : contactsColumnWidthDesktop;
      case 'email address':
        return isTabletOrSmaller
            ? emailColumnWidthTablet
            : emailColumnWidthDesktop;
      case 'join date':
        return isTabletOrSmaller
            ? joinDateColumnWidthTablet
            : joinDateColumnWidthDesktop;
      case 'address':
        return isTabletOrSmaller
            ? addressColumnWidthTablet
            : addressColumnWidthDesktop;
      case 'total agents':
        return isTabletOrSmaller
            ? totalAgentsColumnWidthTablet
            : totalAgentsColumnWidthDesktop;
      case 'total sales volume':
        return isTabletOrSmaller
            ? totalSalesColumnWidthTablet
            : totalSalesColumnWidthDesktop;
      default:
        return isTabletOrSmaller
            ? defaultColumnWidthTablet
            : defaultColumnWidthDesktop;
    }
  }

  Widget _buildScrollableTable(
    Widget dataTable,
    double minTableWidth,
    bool isTabletOrSmaller,
  ) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;
        final needsHorizontalScroll = minTableWidth > availableWidth;

        return Scrollbar(
          controller: _verticalScrollController,
          thumbVisibility: true,
          child: SingleChildScrollView(
            scrollDirection: Axis.vertical,
            controller: _verticalScrollController,
            physics: const ClampingScrollPhysics(),
            child: needsHorizontalScroll
                ? Scrollbar(
                    controller: _horizontalScrollController,
                    thumbVisibility: true,
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      controller: _horizontalScrollController,
                      physics: const ClampingScrollPhysics(),
                      child: SizedBox(width: minTableWidth, child: dataTable),
                    ),
                  )
                : SizedBox(width: availableWidth, child: dataTable),
          ),
        );
      },
    );
  }

  // TODO: PAGINATION_SECTION - Build responsive pagination section
  // TODO: PAGINATION_RESPONSIVE - Optimize pagination for different screen sizes
  // TODO: PAGINATION_INFO - Add configurable pagination info display
  Widget _buildPaginationSection() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTabletOrSmaller = screenWidth < tabletBreakpoint;
    final isMobile = screenWidth < mobileBreakpoint;

    // TODO: PAGINATION_PADDING - Extract padding calculations to constants
    final double horizontalPadding = isTabletOrSmaller
        ? paginationHorizontalPaddingTablet
        : paginationHorizontalPaddingDesktop;
    final double verticalPadding = isTabletOrSmaller
        ? paginationVerticalPaddingTablet
        : paginationVerticalPaddingDesktop;

    // TODO: MOBILE_PAGINATION - Mobile-specific pagination layout
    if (isMobile) {
      return Padding(
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: verticalPadding,
        ),
        child: Column(
          children: [
            // TODO: PAGINATION_TEXT - Pagination info text with dynamic content
            Text(
              '${showing} ${(_currentPage - 1) * widget.itemsPerPage + 1} ${to} ${(_currentPage * widget.itemsPerPage).clamp(1, widget.data.length)} ${of} ${widget.data.length}',
              style: const TextStyle(
                fontSize: paginationTextSizeMobile,
                color: AppTheme.paginationTextColor,
                fontFamily: fontFamily,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: paginationControlsSpacing),
            // TODO: PAGINATION_CONTROLS - Mobile pagination controls
            _buildPaginationControls(isMobile),
          ],
        ),
      );
    } else {
      // Horizontal layout for tablet and desktop
      return Padding(
        padding: EdgeInsets.only(
          bottom: horizontalPadding,
          top: paginationTopPadding,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Text(
                '${showingData} ${(_currentPage - 1) * widget.itemsPerPage + 1} ${to} ${(_currentPage * widget.itemsPerPage).clamp(1, widget.data.length)} ${of} ${widget.data.length} ${entries}',
                style: TextStyle(
                  fontSize: isTabletOrSmaller
                      ? paginationTextSizeMobile
                      : paginationTextSize,
                  color: AppTheme.paginationTextColor,
                  fontFamily: fontFamily,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            _buildPaginationControls(isMobile),
          ],
        ),
      );
    }
  }

  // TODO: PAGINATION_CONTROLS - Build pagination navigation controls
  // TODO: PAGINATION_ACCESSIBILITY - Add keyboard navigation support
  // TODO: PAGINATION_ANIMATION - Add smooth transitions between pages
  Widget _buildPaginationControls(bool isMobile) {
    final maxVisiblePages = isMobile
        ? maxVisiblePagesMobile
        : maxVisiblePagesDesktop;
    final hasMultiplePages = _totalPages > 1;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Previous button
        _buildPaginationButton(
          onPressed: hasMultiplePages && _currentPage > 1
              ? _previousPage
              : null,
          isArrowButton: true,
          child: Icon(
            Icons.chevron_left,
            size: paginationArrowIconSize,
            color: hasMultiplePages && _currentPage > 1
                ? AppTheme.paginationTextColor
                : AppTheme.paginationTextColor.withValues(alpha: 0.3),
          ),
        ),

        const SizedBox(width: paginationButtonGap),

        // Page numbers with ellipsis logic
        ..._buildPageNumbers(maxVisiblePages),

        const SizedBox(width: paginationButtonGap),

        // Next button
        _buildPaginationButton(
          onPressed: hasMultiplePages && _currentPage < _totalPages
              ? _nextPage
              : null,
          isArrowButton: true,
          child: Icon(
            Icons.chevron_right,
            size: paginationArrowIconSize,
            color: hasMultiplePages && _currentPage < _totalPages
                ? AppTheme.paginationTextColor
                : AppTheme.paginationTextColor.withValues(alpha: 0.3),
          ),
        ),
      ],
    );
  }

  // TODO: PAGINATION_BUTTON - Build individual pagination buttons
  // TODO: BUTTON_STATES - Add hover and focus states
  // TODO: BUTTON_THEMING - Make button styling configurable
  Widget _buildPaginationButton({
    required VoidCallback? onPressed,
    required Widget child,
    bool isSelected = false,
    bool isArrowButton = false,
  }) {
    Color backgroundColor;
    if (isSelected) {
      backgroundColor = AppTheme.primaryColor;
    } else if (isArrowButton) {
      backgroundColor = AppTheme.paginationArrowBackgroundColor;
    } else {
      backgroundColor = Colors.transparent;
    }

    return SizedBox(
      width: paginationButtonWidth,
      height: paginationButtonHeight,
      child: TextButton(
        onPressed: onPressed,
        style: TextButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: isSelected
              ? Colors.white
              : AppTheme.paginationTextColor,
          padding: EdgeInsets.zero,
          minimumSize: Size.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              paginationButtonBorderRadius,
            ),
          ),
        ),
        child: Center(child: child),
      ),
    );
  }

  List<Widget> _buildPageNumbers(int maxVisiblePages) {
    List<Widget> pages = [];

    // Handle case when there are no pages (shouldn't happen, but safety check)
    if (_totalPages <= 0) {
      pages.add(_buildPageButton(1));
      return pages;
    }

    if (_totalPages <= maxVisiblePages) {
      // Show all pages if total pages is less than max visible
      for (int i = 1; i <= _totalPages; i++) {
        pages.add(_buildPageButton(i));
        if (i < _totalPages) {
          pages.add(const SizedBox(width: paginationButtonGap));
        }
      }
    } else {
      // Complex pagination with ellipsis
      pages.add(_buildPageButton(1));

      if (_currentPage > 3) {
        pages.add(const SizedBox(width: paginationButtonGap));
        pages.add(_buildEllipsis());
      }

      // Show pages around current page
      int start = (_currentPage - 1).clamp(2, _totalPages - 1);
      int end = (_currentPage + 1).clamp(2, _totalPages - 1);

      for (int i = start; i <= end; i++) {
        if (i != 1 && i != _totalPages) {
          pages.add(const SizedBox(width: paginationButtonGap));
          pages.add(_buildPageButton(i));
        }
      }

      if (_currentPage < _totalPages - 2) {
        pages.add(const SizedBox(width: paginationButtonGap));
        pages.add(_buildEllipsis());
      }

      if (_totalPages > 1) {
        pages.add(const SizedBox(width: paginationButtonGap));
        pages.add(_buildPageButton(_totalPages));
      }
    }

    return pages;
  }

  Widget _buildPageButton(int pageNumber) {
    final isSelected = pageNumber == _currentPage;
    final hasMultiplePages = _totalPages > 1;

    return _buildPaginationButton(
      onPressed: hasMultiplePages ? () => _goToPage(pageNumber) : null,
      isSelected: isSelected,
      child: Text(
        pageNumber.toString(),
        style: TextStyle(
          fontSize: paginationTextSize,
          fontFamily: fontFamily,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          color: isSelected ? Colors.white : AppTheme.paginationTextColor,
        ),
      ),
    );
  }

  // TODO: ELLIPSIS_WIDGET - Build ellipsis indicator for pagination
  // TODO: ELLIPSIS_STYLING - Make ellipsis styling configurable
  Widget _buildEllipsis() {
    return SizedBox(
      width: paginationButtonWidth,
      height: paginationButtonHeight,
      child: Center(
        child: Text(
          ellipsisText,
          style: TextStyle(
            fontSize: paginationTextSize,
            fontFamily: fontFamily,
            color: AppTheme.paginationTextColor,
          ),
        ),
      ),
    );
  }
}

// TODO: HELPER_CLASSES - Helper classes are defined in table_constants.dart
// TODO: REFACTORING - Consider extracting helper classes to separate files for better organization
// - TableColumn: Column configuration with custom builders and alignment
// - TableAction: Action button configuration for table rows
// - TableFilter: Filter configuration for data filtering
// - TableContainerTitle: Title configuration with icons and styling
