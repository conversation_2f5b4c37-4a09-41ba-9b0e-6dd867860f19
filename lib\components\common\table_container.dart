import 'package:flutter/material.dart';
import 'package:neorevv/config/app_strings.dart';
import 'package:neorevv/config/broker_table_config.dart';
import 'package:neorevv/config/constants.dart';
import 'package:neorevv/theme/app_theme.dart';
import 'generic_data_table.dart';

/// TODO: CONTAINER_DESIGN - Main table container widget with responsive design
/// - Handles responsive layout for desktop, tablet, and mobile
/// - Provides consistent styling and spacing
/// - Manages header section with title and filter options
/// - Wraps GenericDataTable with proper container styling
class TableContainer extends StatelessWidget {
  // TODO: PROPERTIES - Header and display configuration
  final TableContainerTitle? title;
  final List<Widget>? filterOptions;

  // TODO: PROPERTIES - Core table data and structure
  final List<TableColumn> columns;
  final List<Map<String, dynamic>> data;
  final List<TableAction>? actions;
  final List<TableFilter>? filters;

  // TODO: PROPERTIES - Pagination and display settings
  final int itemsPerPage;
  final String? emptyMessage;
  final bool showPagination;
  final bool showSearch;
  final String? searchHint;
  final List<String>? searchableFields;

  // TODO: PROPERTIES - Event handlers
  final void Function(String)? onSearch;
  final void Function(String, String?)? onFilter;
  final void Function(String, bool)? onSort;

  // TODO: PROPERTIES - Container styling and layout
  final double? width;
  final double? height;
  final EdgeInsets? padding;
  final BoxDecoration? decoration;

  const TableContainer({
    super.key,
    this.title,
    this.filterOptions,
    required this.columns,
    required this.data,
    this.actions,
    this.filters,
    this.itemsPerPage = 10,
    this.emptyMessage,
    this.showPagination = true,
    this.showSearch = false,
    this.searchHint,
    this.searchableFields,
    this.onSearch,
    this.onFilter,
    this.onSort,
    this.width,
    this.height,
    this.padding,
    this.decoration,
  });

  // TODO: RESPONSIVE_DESIGN - Make breakpoints configurable
  // TODO: RESPONSIVE_DESIGN - Add tablet-specific optimizations
  // TODO: CONTAINER_LAYOUT - Extract responsive calculations to separate methods
  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isWeb = screenSize.width > 800;
    final isTabletOrSmaller = screenSize.width < 1000;

    // TODO: RESPONSIVE_CALCULATIONS - Extract to separate method for reusability
    final containerHeight = isWeb
        ? (screenSize.height * 0.75).clamp(600.0, double.infinity)
        : null;

    // TODO: RESPONSIVE_MARGINS - Make margins configurable via constants
    final horizontalMargin = isTabletOrSmaller ? 12.0 : 54.0;
    final verticalMargin = isTabletOrSmaller ? 8.0 : 54.0;

    return   Container(
        width: double.infinity,
        height: containerHeight,
        margin: EdgeInsets.symmetric(
          horizontal: horizontalMargin,
          vertical: verticalMargin,
        ),
        // TODO: CONTAINER_PADDING - Extract padding calculations to constants
        padding:
            padding ??
            EdgeInsets.only(
              left: isTabletOrSmaller ? 12 :  containerPaddingLeft,
              right: isTabletOrSmaller ? 12 : containerPaddingLeft,
              top: isTabletOrSmaller ? 12 : containerPadding,
            ),
        // TODO: CONTAINER_STYLING - Make shadow and styling configurable
        decoration:
            decoration ??
            BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(borderRadius),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // TODO: HEADER_SECTION - Add conditional header visibility controls
            if (title != null || filterOptions != null) ...[
              _buildHeaderSection(context, isWeb),
              SizedBox(height: isTabletOrSmaller ? 8 : 10),
            ],

            // TODO: TABLE_INTEGRATION - Add loading states and error handling
            Expanded(
              child: GenericDataTable(
                columns: columns,
                data: data,
                actions: actions,
                filters: filters,
                itemsPerPage: itemsPerPage,
                emptyMessage: emptyMessage,
                showPagination: showPagination,
                showSearch: showSearch,
                searchHint: searchHint,
                searchableFields: searchableFields,
                onSearch: onSearch,
                onFilter: onFilter,
                onSort: onSort,
              ),
            ),
          ],
        ),
       
    );
  }

  // TODO: HEADER_LAYOUT - Responsive header with vertical/horizontal layouts
  // TODO: HEADER_BREAKPOINT - Make breakpoint configurable via constants
  // TODO: HEADER_ALIGNMENT - Add configurable alignment options
  Widget _buildHeaderSection(BuildContext context, bool isWeb) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTabletOrSmaller =
        screenWidth < 1000; // TODO: BREAKPOINT - Extract to constants

    return Column(
      children: [
        // TODO: MOBILE_LAYOUT - Optimize mobile header layout
        if (isTabletOrSmaller) ...[
          // TODO: TITLE_MOBILE - Add title styling options for mobile
          if (title != null) ...[
            Align(alignment: Alignment.centerLeft, child: _buildTitle()),
            const SizedBox(height: 10), // TODO: SPACING - Extract to constants
          ],

          // TODO: FILTER_MOBILE - Add mobile filter optimization
          if (filterOptions != null) ...[
            Align(
              alignment: Alignment.centerRight,
              child: Wrap(
                spacing: 8, // TODO: SPACING - Extract to constants
                runSpacing: 6, // TODO: SPACING - Extract to constants
                children: filterOptions!,
              ),
            ),
          ],
        ] else ...[
          // TODO: DESKTOP_LAYOUT - Add desktop layout customization options
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // TODO: TITLE_DESKTOP - Add title flex configuration
              if (title != null)
                Expanded(
                  flex: 2, // TODO: FLEX_RATIO - Make flex ratios configurable
                  child: _buildTitle(),
                ),

              // Filter Options Section
              if (filterOptions != null)
                Expanded(
                  flex: 3,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: filterOptions!.map((filter) {
                      final index = filterOptions!.indexOf(filter);
                      return Padding(
                        padding: EdgeInsets.only(left: index > 0 ? 12 : 0),
                        child: filter,
                      );
                    }).toList(),
                  ),
                ),
            ],
          ),
        ],
      ],
    );
  }

  // TODO: TITLE_BUILDER - Add title animation and transition effects
  // TODO: TITLE_STYLING - Make title styling more configurable
  // TODO: TITLE_ICONS - Add icon size configuration options
  Widget _buildTitle() {
    // TODO: TITLE_DEFAULTS - Extract default styling to constants
    final defaultStyle = TextStyle(
      fontSize: titleFontSize,
      fontWeight: titleFontWeight,
      fontFamily: fontFamily,
      color: AppTheme.primaryTableText,
    );

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // TODO: CUSTOM_ICONS - Add icon validation and error handling
        if (title!.customIcon != null) ...[
          title!.customIcon!,
          const SizedBox(width: 12), // TODO: SPACING - Extract to constants
        ] else if (title!.icon != null) ...[
          Icon(
            title!.icon,
            size: 24, // TODO: ICON_SIZE - Make configurable
            color: AppTheme.primaryTableText,
          ),
          const SizedBox(width: 12), // TODO: SPACING - Extract to constants
        ],
        Text(title!.text, style: title!.style ?? defaultStyle),
        // TODO: TRAILING_WIDGETS - Add trailing widget positioning options
        if (title!.trailing != null) ...[
          const SizedBox(width: 12), // TODO: SPACING - Extract to constants
          title!.trailing!,
        ],
      ],
    );
  }
}

// TODO: HELPER_WIDGETS - Extract helper widgets to separate files
/// TODO: FILTER_BUTTON - Reusable filter button component
/// - Supports selected/unselected states
/// - Configurable icon and label
/// - Consistent styling with table theme
class FilterButton extends StatelessWidget {
  // TODO: FILTER_PROPERTIES - Add more customization options
  final String label;
  final IconData? icon;
  final VoidCallback? onPressed;
  final bool isSelected;

  const FilterButton({
    super.key,
    required this.label,
    this.icon,
    this.onPressed,
    this.isSelected = false,
  });

  // TODO: FILTER_BUTTON_BUILD - Add hover effects and animations
  // TODO: FILTER_BUTTON_STYLING - Extract styling to theme constants
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40, // TODO: BUTTON_HEIGHT - Make configurable
      decoration: BoxDecoration(
        border: Border.all(
          color: isSelected
              ? AppTheme.primaryColor
              : const Color(0xFFE9ECEF), // TODO: COLORS - Extract to constants
        ),
        borderRadius: BorderRadius.circular(
          8,
        ), // TODO: BORDER_RADIUS - Make configurable
        color: isSelected
            ? AppTheme.primaryColor.withValues(alpha: 0.1)
            : Colors.white,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(
            8,
          ), // TODO: BORDER_RADIUS - Use same constant
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 10,
            ), // TODO: PADDING - Extract to constants
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // TODO: ICON_SECTION - Add icon size configuration
                if (icon != null) ...[
                  Icon(
                    icon,
                    size: 16, // TODO: ICON_SIZE - Make configurable
                    color: isSelected
                        ? AppTheme.primaryColor
                        : const Color(
                            0xFF6C757D,
                          ), // TODO: COLORS - Extract to constants
                  ),
                  const SizedBox(
                    width: 8,
                  ), // TODO: SPACING - Extract to constants
                ],
                // TODO: TEXT_SECTION - Add text styling configuration
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected
                        ? AppTheme.primaryColor
                        : const Color(
                            0xFF6C757D,
                          ), // TODO: COLORS - Extract to constants
                    fontSize: 14, // TODO: FONT_SIZE - Make configurable
                    fontWeight: FontWeight.w500,
                    fontFamily: fontFamily,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// TODO: SEARCH_FIELD - Reusable search field component
/// - Responsive width calculation
/// - Consistent styling with table theme
/// - Configurable hint text and callbacks
class SearchField extends StatelessWidget {
  // TODO: SEARCH_PROPERTIES - Add more customization options (icons, validation, etc.)
  final String? hintText;
  final double? width;
  final void Function(String)? onChanged;

  const SearchField({super.key, this.hintText, this.width, this.onChanged});

  // TODO: SEARCH_BUILD - Add focus states and animations
  // TODO: SEARCH_RESPONSIVE - Extract responsive logic to separate method
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTabletOrSmaller =
        screenWidth < 1000; // TODO: BREAKPOINT - Use constants
    final isMobile = screenWidth < 600; // TODO: BREAKPOINT - Use constants

    // TODO: RESPONSIVE_WIDTH - Extract width calculation logic
    final fieldWidth =
        width ??
        (isMobile
            ? screenWidth * 0.8
            : // TODO: WIDTH_RATIO - Make configurable
              isTabletOrSmaller
            ? 200
            : 300 // TODO: WIDTH_VALUES - Extract to constants
              );

    return Container(
      width: fieldWidth,
      height: 40,
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE9ECEF), width: 1),
      ),
      child: TextField(
        onChanged: onChanged,
        style: const TextStyle(
          fontFamily: fontFamily,
          fontSize: 14,
        ),
        decoration: InputDecoration(
          hintText: hintText ?? 'Search',
          hintStyle: const TextStyle(
            color: Color(0xFF6C757D),
            fontSize: 14,
            fontFamily: fontFamily,
          ),
          prefixIcon: const Icon(
            Icons.search,
            size: 18,
            color: Color(0xFF6C757D),
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: isTabletOrSmaller ? 12 : 16,
            vertical: 10,
          ),
        ),
      ),
    );
  }
}
