import 'package:flutter/material.dart';
import '../../../theme/app_theme.dart';
import '/config/constants.dart';

class AgentsTable extends StatelessWidget {
  const AgentsTable({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.person, size: 20),
              const SizedBox(width: 10),
              const Text(
                "Agents",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              SizedBox(
                width: 200,
                child: TextField(
                  decoration: InputDecoration(
                    hintText: "Search by name",
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.grey[100],
                    contentPadding: const EdgeInsets.symmetric(vertical: 0),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              columnSpacing: defaultPadding,
              columns: const [
                DataColumn(
                  label: Row(
                    children: [
                      Text("Agent Name"),
                      Icon(Icons.arrow_upward, size: 16),
                    ],
                  ),
                ),
                DataColumn(
                  label: Row(
                    children: [
                      Text("Related Broker"),
                      Icon(Icons.arrow_upward, size: 16),
                    ],
                  ),
                ),
                DataColumn(
                  label: Row(
                    children: [
                      Text("Contacts"),
                      Icon(Icons.arrow_upward, size: 16),
                    ],
                  ),
                ),
                DataColumn(
                  label: Row(
                    children: [
                      Text("Commission"),
                      Icon(Icons.arrow_upward, size: 16),
                    ],
                  ),
                ),
                DataColumn(
                  label: Row(
                    children: [
                      Text("Revenue"),
                      Icon(Icons.arrow_upward, size: 16),
                    ],
                  ),
                ),
                DataColumn(
                  label: Row(
                    children: [
                      Text("Status"),
                      Icon(Icons.arrow_upward, size: 16),
                    ],
                  ),
                ),
                DataColumn(label: Text("Actions")),
              ],
              rows: List.generate(5, (index) => _agentDataRow(context, index)),
            ),
          ),
          const SizedBox(height: defaultPadding),
          Row(
            children: [
              Text(
                "Showing data 1 to 5 of 255 entries",
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const Spacer(),
              _buildPagination(),
            ],
          ),
        ],
      ),
    );
  }

  DataRow _agentDataRow(BuildContext context, int index) {
    final agents = [
      "Jane Cooper",
      "Floyd Miles",
      "Ronald Richards",
      "Marvin McKinney",
      "Jerome Bell",
    ];

    final brokers = [
      "Broker 1",
      "Broker 2",
      "Broker 1",
      "Broker 1",
      "Broker 2",
    ];

    final statuses = [true, false, true, false, true];

    return DataRow(
      cells: [
        DataCell(
          Row(
            children: [
              const CircleAvatar(
                radius: 15,
                backgroundColor: Colors.blue,
                child: Icon(Icons.person, color: Colors.white, size: 15),
              ),
              const SizedBox(width: 10),
              Text(agents[index]),
            ],
          ),
        ),
        DataCell(
          Row(
            children: [
              const CircleAvatar(
                radius: 15,
                backgroundColor: Colors.blue,
                child: Icon(Icons.business, color: Colors.white, size: 15),
              ),
              const SizedBox(width: 10),
              Text(brokers[index]),
            ],
          ),
        ),
        const DataCell(Text("(*************")),
        DataCell(Text("${(index + 2) % 5 + 2}%")),
        const DataCell(Text("\$5,447.00")),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            decoration: BoxDecoration(
              color: statuses[index]
                  ? Colors.green.withOpacity(0.2)
                  : Colors.red.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              statuses[index] ? "Paid" : "Not paid",
              style: TextStyle(
                color: statuses[index] ? Colors.green : Colors.red,
                fontSize: 12,
              ),
            ),
          ),
        ),
        DataCell(
          TextButton.icon(
            onPressed: () {},
            icon: const Icon(Icons.visibility, color: Colors.blue, size: 16),
            label: const Text(
              "View documents",
              style: TextStyle(color: Colors.blue, fontSize: 12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPagination() {
    return Row(
      children: [
        _paginationButton(icon: Icons.chevron_left),
        _paginationButton(label: "1", isSelected: true),
        _paginationButton(label: "2"),
        _paginationButton(label: "3"),
        _paginationButton(label: "4"),
        _paginationButton(label: "..."),
        _paginationButton(label: "40"),
        _paginationButton(icon: Icons.chevron_right),
      ],
    );
  }

  Widget _paginationButton({
    String? label,
    IconData? icon,
    bool isSelected = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      width: 30,
      height: 30,
      decoration: BoxDecoration(
        color: isSelected ? AppTheme.primaryColor : Colors.transparent,
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Center(
        child: icon != null
            ? Icon(
                icon,
                size: 16,
                color: isSelected ? Colors.white : Colors.black,
              )
            : Text(
                label!,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.black,
                  fontSize: 12,
                ),
              ),
      ),
    );
  }
}
