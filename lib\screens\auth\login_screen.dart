import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../theme/app_theme.dart';
import '/config/app_strings.dart';
import '/config/constants.dart';
import '/config/responsive.dart';
import '/theme/app_fonts.dart';
import '/screens/auth/components/auth_background.dart';
import '/screens/auth/components/social_login_button.dart';
import '/screens/auth/components/custom_text_field.dart';
import '/screens/auth/components/custom_checkbox.dart';
import '/screens/auth/components/primary_button.dart';
import '/screens/dashboard/dashboard_screen.dart';

class LoginScreen extends HookWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // State management using hooks
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final obscurePassword = useState(true);
    final rememberMeState = useState(false);

    final size = MediaQuery.of(context).size;
    return Scaffold(
      body: AuthBackground(
        child: Center(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: Responsive.isMobile(context) ? double.infinity : 900,
              minHeight: Responsive.isMobile(context)
                  ? size.height * 0.8
                  : size.height * 0.6,
            ),

            child: SingleChildScrollView(
              child: Card(
                color: Colors.transparent,
                elevation: Responsive.isMobile(context) ? 0 : 10,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    Responsive.isMobile(context) ? 0 : 5,
                  ),
                ),
                margin: EdgeInsets.symmetric(
                  horizontal: Responsive.isMobile(context) ? 0 : 40,
                  vertical: Responsive.isMobile(context) ? 0 : 20,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                    Responsive.isMobile(context) ? 0 : 5,
                  ),
                  child: IntrinsicHeight(
                    child: Responsive.isMobile(context)
                        ? LoginRightPanel(
                            isMobile: true,
                            emailController: emailController,
                            passwordController: passwordController,
                            obscurePassword: obscurePassword,
                            rememberMeState: rememberMeState,
                          ) // Full width on mobile
                        : Row(
                            children: [
                              // Left side - Blue panel with logo and description
                              if (!Responsive.isSmallMobile(context))
                                Expanded(
                                  flex: Responsive.isTablet(context) ? 4 : 5,
                                  child: const LoginLeftPanel(),
                                ),

                              // Right side - Login form
                              Expanded(
                                flex: Responsive.isTablet(context) ? 6 : 5,
                                child: LoginRightPanel(
                                  isMobile: false,
                                  emailController: emailController,
                                  passwordController: passwordController,
                                  obscurePassword: obscurePassword,
                                  rememberMeState: rememberMeState,
                                ),
                              ),
                            ],
                          ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class LoginLeftPanel extends StatelessWidget {
  const LoginLeftPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(defaultPadding * 2),
      decoration: BoxDecoration(
        color: AppTheme.loginBgColor.withValues(alpha: 0.65),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 2,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Title with icon
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                '$iconAssetpath/login_title_icon.png',
                height: 35,
                width: 35,
              ),
              const SizedBox(width: 12),
              Text(
                appName,
                textAlign: TextAlign.center,
                style: AppFonts.boldTextStyle(28, color: Colors.white),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),

          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: AppFonts.normalTextStyle(
                20,
                color: Colors.white,
              ).copyWith(height: 1.5),
              children: [
                TextSpan(text: appDescriptionP1),
                TextSpan(
                  text: appDescriptionP2,
                  style: AppFonts.semiBoldTextStyle(20, color: Colors.white),
                ),
                TextSpan(text: appDescriptionP3),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class LoginRightPanel extends StatelessWidget {
  final bool isMobile;
  final TextEditingController emailController;
  final TextEditingController passwordController;
  final ValueNotifier<bool> obscurePassword;
  final ValueNotifier<bool> rememberMeState;

  const LoginRightPanel({
    super.key,
    required this.isMobile,
    required this.emailController,
    required this.passwordController,
    required this.obscurePassword,
    required this.rememberMeState,
  });

  void _navigateToDashboard(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const DashboardScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 300;

    return Container(
      padding: EdgeInsets.all(
        Responsive.isMobile(context) ? defaultPadding : defaultPadding * 2,
      ),
      margin: EdgeInsets.all(
        Responsive.isMobile(context) ? defaultPadding * 2 : 0,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(
          Responsive.isMobile(context) ? 5 : 0,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Show logo only on small mobile devices
          if (Responsive.isMobile(context)) ...[
            Text(
              appName,
              style: AppFonts.boldTextStyle(28, color: AppTheme.roundIconColor),
            ),
            const SizedBox(height: defaultPadding * 2),
          ],

          Text(
            loginTitle,
            style: AppFonts.semiBoldTextStyle(
              24,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: defaultPadding * 2),

          SocialLoginButton(
            icon: Image.asset(
              '$iconAssetpath/google.png',
              height: 20,
              width: 20,
            ),
            text: signInWithGmail,
            onPressed: () {
              _navigateToDashboard(context);
            },
          ),

          const SizedBox(height: defaultPadding * 1.8),

          Text(
            orContinueWith,
            style: AppFonts.regularTextStyle(
              14,
              color: AppTheme.orContinueWithColor,
            ),
          ),
          const SizedBox(height: defaultPadding * 1.8),

          // Email input field
          CustomTextField(
            controller: emailController,
            hintText: emailHint,
            keyboardType: TextInputType.emailAddress,
          ),

          const SizedBox(height: defaultPadding),

          // Password input field
          CustomTextField(
            controller: passwordController,
            hintText: passwordHint,
            obscureText: obscurePassword.value,
            suffixIcon: IconButton(
              icon: Icon(
                obscurePassword.value ? Icons.visibility_off : Icons.visibility,
                color: Colors.grey,
                size: 20,
              ),
              onPressed: () {
                obscurePassword.value = !obscurePassword.value;
              },
            ),
          ),

          const SizedBox(height: 8),

          // Remember me checkbox and forgot password
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomCheckbox(
                value: rememberMeState.value,
                onChanged: (value) {
                  rememberMeState.value = value ?? false;
                },
                text: rememberMe,
              ),
              GestureDetector(
                onTap: () {
                  // Handle forgot password
                },
                child: Text(
                  forgotPassword,
                  style: AppFonts.mediumTextStyle(
                    14,
                    color: AppTheme.roundIconColor,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: defaultPadding * 1.5),

          // Login button
          PrimaryButton(
            text: loginButton,
            height: 45,
            borderRadius: 25,
            onPressed: () {
              _navigateToDashboard(context);
            },
          ),

          const SizedBox(height: defaultPadding * 2),

          // Sign up link
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: dontHaveAccount,
                  style: AppFonts.regularTextStyle(14, color: Colors.black54),
                ),
                WidgetSpan(
                  alignment: PlaceholderAlignment.middle,
                  child: GestureDetector(
                    onTap: () {
                      // Navigate to sign up screen
                    },
                    child: Text(
                      isSmallScreen ? "\n$signUp" : signUp,
                      style: AppFonts.semiBoldTextStyle(
                        14,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
