import 'package:flutter/material.dart';
import 'package:neorevv/theme/app_theme.dart';
import '/config/constants.dart';

class SideMenu extends StatelessWidget {
  const SideMenu({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: Colors.white,
      child: ListView(
        children: [
          const DrawerHeader(
            child: Center(
              child: Text(
                "NeoRevv",
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24),
              ),
            ),
          ),
          DrawerListTile(
            title: "Dashboard",
            icon: Icons.dashboard,
            isSelected: true,
            onTap: () {},
          ),
          DrawerListTile(title: "Brokers", icon: Icons.people, onTap: () {}),
          DrawerListTile(title: "Agents", icon: Icons.person, onTap: () {}),
          DrawerListTile(
            title: "Sales",
            icon: Icons.shopping_cart,
            onTap: () {},
          ),
          DrawerListTile(title: "Commission", icon: Icons.money, onTap: () {}),
        ],
      ),
    );
  }
}

class DrawerListTile extends StatelessWidget {
  final String title;
  final IconData icon;
  final VoidCallback onTap;
  final bool isSelected;

  const DrawerListTile({
    Key? key,
    required this.title,
    required this.icon,
    required this.onTap,
    this.isSelected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onTap,
      horizontalTitleGap: 0.0,
      selected: isSelected,
      selectedTileColor: Colors.blue.withOpacity(0.1),
      leading: Icon(
        icon,
        color: isSelected ? AppTheme.primaryColor : Colors.grey,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? AppTheme.primaryColor : Colors.grey,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }
}
