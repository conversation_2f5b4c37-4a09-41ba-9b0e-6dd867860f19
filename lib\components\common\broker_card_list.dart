import 'package:flutter/material.dart';
import 'package:neorevv/config/app_strings.dart';
import 'package:neorevv/config/broker_data.dart';
import 'package:neorevv/theme/app_theme.dart';

class BrokerCardList extends StatelessWidget {
  final List<BrokerData> brokers;

  const BrokerCardList({
    super.key,
    required this.brokers,
  });

  @override
  Widget build(BuildContext context) {
    if (brokers.isEmpty) {
      return const Center(
        child: Text(
          'No brokers found',
          style: TextStyle(
            color: AppTheme.secondaryText,
            fontSize: 16,
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: brokers.length,
      itemBuilder: (context, index) {
        final broker = brokers[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: BrokerCard(broker: broker),
        );
      },
    );
  }
}

class BrokerCard extends StatelessWidget {
  final BrokerData broker;

  const BrokerCard({
    super.key,
    required this.broker,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with name and action button
            Row(
              children: [
                // Avatar
                CircleAvatar(
                  radius: 20,
                  backgroundColor: AppTheme.primaryColor,
                  child: Text(
                    broker.brokerName.split(' ').map((e) => e[0]).join(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontFamily: fontFamily ,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Name
                Expanded(
                  child: Text(
                    broker.brokerName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontFamily: fontFamily ,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
                
                // Action button
                IconButton(
                  onPressed: () {},
                  icon: const Icon(
                    Icons.visibility_outlined,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Contact information
            _buildInfoRow(
              icon: Icons.phone_outlined,
              label: 'Contact',
              value: broker.contacts,
            ),
            
            const SizedBox(height: 12),
            
            _buildInfoRow(
              icon: Icons.email_outlined,
              label: 'Email',
              value: broker.emailAddress,
            ),
            
            const SizedBox(height: 12),
            
            _buildInfoRow(
              icon: Icons.calendar_today_outlined,
              label: 'Join Date',
              value: broker.joinDate,
            ),
            
            const SizedBox(height: 12),
            
            _buildInfoRow(
              icon: Icons.location_on_outlined,
              label: 'Address',
              value: broker.address,
            ),
            
            const SizedBox(height: 16),
            
            // Stats row
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Agents',
                    broker.totalAgents.toString(),
                    AppTheme.primaryColor,
                  ),
                ),
                
                const SizedBox(width: 12),
                
                Expanded(
                  child: _buildStatCard(
                    'Sales Volume',
                    broker.totalSalesVolume,
                    AppTheme.successColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 16,
          color: AppTheme.secondaryText,
        ),
        
        const SizedBox(width: 8),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  color: AppTheme.secondaryText,
                  fontWeight: FontWeight.w500,
                  fontFamily:fontFamily ,
                ),
              ),
              
              const SizedBox(height: 2),
              
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w500,
                  fontFamily:fontFamily ,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
              fontFamily:fontFamily ,
            ),
          ),
          
          const SizedBox(height: 4),
          
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              color: color,
              fontWeight: FontWeight.w700,
            ),
          ),
        ],
      ),
    );
  }
}
